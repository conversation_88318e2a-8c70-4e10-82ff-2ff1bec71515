#!/usr/bin/env python3
"""
分析fail-logs日志文件，提取每个trigger symbol对应的日志时间、u_ts、e_ts、from四个字段
"""

import re
import json
from datetime import datetime
from typing import List, Dict, Any
import argparse


def parse_log_file(file_path: str) -> List[Dict[str, Any]]:
    """
    解析日志文件，提取trigger symbol相关信息
    
    Args:
        file_path: 日志文件路径
        
    Returns:
        包含解析结果的列表，每个元素包含trigger symbol及其相关交易对信息
    """
    results = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 查找trigger symbol行
        trigger_match = re.search(r'Trigger symbol: (\w+)', line)
        if trigger_match:
            trigger_symbol = trigger_match.group(1)
            
            # 提取当前行的时间戳
            time_match = re.search(r'\[([^\]]+)\]', line)
            log_time = time_match.group(1) if time_match else ""
            
            # 向前查找相关的交易对信息
            trading_pairs = []
            j = i - 1
            
            # 向前搜索，直到找到"Total latency"行或到达文件开头
            while j >= 0:
                prev_line = lines[j].strip()
                
                # 如果遇到"Total latency"行，说明这是当前套利操作的开始
                if "Total latency:" in prev_line:
                    break
                
                # 查找包含交易对信息的行 (格式: SYMBOL dir: BUY/SELL p: price q: quantity u_ts: timestamp e_ts: timestamp from: number)
                pair_match = re.search(
                    r'(\w+) dir: (BUY|SELL) p: ([\d.]+) q: ([\d.]+) u_ts: (\d+) e_ts: (\d+) from: (\d+)',
                    prev_line
                )
                
                if pair_match:
                    symbol = pair_match.group(1)
                    direction = pair_match.group(2)
                    price = float(pair_match.group(3))
                    quantity = float(pair_match.group(4))
                    u_ts = int(pair_match.group(5))
                    e_ts = int(pair_match.group(6))
                    from_value = int(pair_match.group(7))
                    
                    # 提取该行的时间戳
                    time_match = re.search(r'\[([^\]]+)\]', prev_line)
                    pair_log_time = time_match.group(1) if time_match else ""
                    
                    trading_pairs.append({
                        'symbol': symbol,
                        'direction': direction,
                        'price': price,
                        'quantity': quantity,
                        'u_ts': u_ts,
                        'e_ts': e_ts,
                        'from': from_value,
                        'log_time': pair_log_time
                    })
                
                j -= 1
            
            # 将交易对列表反转，因为我们是从后往前搜索的
            trading_pairs.reverse()
            
            # 添加到结果中
            result_entry = {
                'trigger_symbol': trigger_symbol,
                'trigger_log_time': log_time,
                'trading_pairs': trading_pairs,
                'total_pairs': len(trading_pairs)
            }
            
            results.append(result_entry)
        
        i += 1
    
    return results


def format_timestamp(timestamp: int) -> str:
    """
    将时间戳转换为可读格式
    
    Args:
        timestamp: 时间戳（微秒）
        
    Returns:
        格式化的时间字符串
    """
    try:
        # 假设时间戳是微秒级别的
        dt = datetime.fromtimestamp(timestamp / 1000000)
        return dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # 保留毫秒
    except:
        return str(timestamp)


def print_analysis_summary(results: List[Dict[str, Any]]):
    """
    打印分析摘要
    
    Args:
        results: 解析结果列表
    """
    print(f"\n=== 日志分析摘要 ===")
    print(f"总共找到 {len(results)} 个trigger symbol事件")
    
    # 统计每个trigger symbol的出现次数
    trigger_counts = {}
    for result in results:
        symbol = result['trigger_symbol']
        trigger_counts[symbol] = trigger_counts.get(symbol, 0) + 1
    
    print(f"\nTrigger Symbol 统计:")
    for symbol, count in sorted(trigger_counts.items()):
        print(f"  {symbol}: {count} 次")
    
    # 统计交易对信息
    all_symbols = set()
    for result in results:
        for pair in result['trading_pairs']:
            all_symbols.add(pair['symbol'])
    
    print(f"\n涉及的交易对总数: {len(all_symbols)}")
    print(f"交易对列表: {', '.join(sorted(all_symbols))}")


def save_to_csv(results: List[Dict[str, Any]], output_file: str):
    """
    将结果保存为CSV文件
    
    Args:
        results: 解析结果列表
        output_file: 输出文件路径
    """
    import csv
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'trigger_symbol', 'trigger_log_time', 'pair_symbol', 'pair_log_time',
            'direction', 'price', 'quantity', 'u_ts', 'e_ts', 'from',
            'u_ts_formatted', 'e_ts_formatted'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in results:
            trigger_symbol = result['trigger_symbol']
            trigger_log_time = result['trigger_log_time']
            
            for pair in result['trading_pairs']:
                writer.writerow({
                    'trigger_symbol': trigger_symbol,
                    'trigger_log_time': trigger_log_time,
                    'pair_symbol': pair['symbol'],
                    'pair_log_time': pair['log_time'],
                    'direction': pair['direction'],
                    'price': pair['price'],
                    'quantity': pair['quantity'],
                    'u_ts': pair['u_ts'],
                    'e_ts': pair['e_ts'],
                    'from': pair['from'],
                    'u_ts_formatted': format_timestamp(pair['u_ts']),
                    'e_ts_formatted': format_timestamp(pair['e_ts'])
                })


def main():
    parser = argparse.ArgumentParser(description='分析fail-logs日志文件')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('--output', '-o', help='输出CSV文件路径', default='analysis_result.csv')
    parser.add_argument('--json', help='输出JSON文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    print(f"正在分析日志文件: {args.log_file}")
    
    # 解析日志文件
    results = parse_log_file(args.log_file)
    
    # 打印摘要
    print_analysis_summary(results)
    
    # 保存为CSV
    save_to_csv(results, args.output)
    print(f"\n结果已保存到CSV文件: {args.output}")
    
    # 如果指定了JSON输出
    if args.json:
        with open(args.json, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到JSON文件: {args.json}")
    
    # 如果启用详细模式，打印每个trigger symbol的详细信息
    if args.verbose:
        print(f"\n=== 详细信息 ===")
        for i, result in enumerate(results, 1):
            print(f"\n{i}. Trigger Symbol: {result['trigger_symbol']}")
            print(f"   触发时间: {result['trigger_log_time']}")
            print(f"   相关交易对数量: {result['total_pairs']}")
            
            for j, pair in enumerate(result['trading_pairs'], 1):
                print(f"   {j}. {pair['symbol']} ({pair['direction']})")
                print(f"      日志时间: {pair['log_time']}")
                print(f"      价格: {pair['price']}, 数量: {pair['quantity']}")
                print(f"      u_ts: {pair['u_ts']} ({format_timestamp(pair['u_ts'])})")
                print(f"      e_ts: {pair['e_ts']} ({format_timestamp(pair['e_ts'])})")
                print(f"      from: {pair['from']}")


if __name__ == "__main__":
    main()
